import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../services/api_service.dart';
import 'package:tareek/presentation/user_homepage_screen/event_details_screen.dart';
import 'news_details_screen.dart';

class DiscoverScreen extends StatefulWidget {
  @override
  _DiscoverScreenState createState() => _DiscoverScreenState();
}

class _DiscoverScreenState extends State<DiscoverScreen> {
  bool isFavorite = false;
  bool isLoading = false;
  bool _isLiked = false;
  int _eventLikesCount = 0;
  bool _isLiking = false;
  int selectedButton = 0;
  late Map<dynamic, dynamic> _user = {};
  String? _userId;
  List<dynamic> _events = [];
  List<dynamic> _news = [];
  List<dynamic> _jobs = [];
  List<dynamic> _jobTypes = [];
  bool _isLoadingNews = true;
  bool _isLoadingEvents = true;
  bool _isLoadingJobs = true;
  bool _isLoadingJobTypes = true;
  List<dynamic> _newsCategories = [];
  List<dynamic> _eventCategories = [];
  bool _isLoadingNewsCategories = true;
  bool _isLoadingEventCategories = true;
  String eventId = '';
  String newsId = '';

  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  // Search and filter variables
  TextEditingController _searchController = TextEditingController();
  String _selectedCategoryId = '';
  String _selectedJobType = '';
  String _searchQuery = '';

  ScrollController _scrollController = ScrollController();

  // Function to handle button selection
  void onButtonPressed(int index) {
    setState(() {
      selectedButton = index;
      _currentPage = 1;
      _hasMoreData = true;
      _searchQuery = '';
      _selectedCategoryId = '';
      _selectedJobType = '';
      _searchController.clear();
    });
    switch (index) {
      case 0:
        _jobs.clear();
        getJobs(isRefresh: true);
        break;
      case 1:
        _news.clear();
        getNews(isRefresh: true);
        break;
      case 2:
        _events.clear();
        getEvents(isRefresh: true);
        break;
    }
  }

  void initState() {
    super.initState();
    _loadUserData();
    getNews();
    getNewsCategories();
    getEventCategories();
    getJobTypes();
    getEvents();
    _checkIfUserLikedEvent(eventId);

    _scrollController.addListener(_scrollListener);
  }


  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (_hasMoreData && !_isLoadingMore) {
        _loadMoreData();
      }
    }
  }

    void _loadMoreData() {
    if (!_hasMoreData || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });

    switch (selectedButton) {
      case 0:
        getJobs();
        break;
      case 1:
        getNews();
        break;
      case 2:
        getEvents();
        break;
    }
  }

  void _performSearch() {
    setState(() {
      _currentPage = 1;
      _hasMoreData = true;
      _searchQuery = _searchController.text;
    });

    switch (selectedButton) {
      case 0:
        _jobs.clear();
        getJobs(isRefresh: true);
        break;
      case 1:
        _news.clear();
        getNews(isRefresh: true);
        break;
      case 2:
        _events.clear();
        getEvents(isRefresh: true);
        break;
    }
  }
   void _filterByCategory(String categoryId) {
    setState(() {
      _selectedCategoryId = categoryId;
      _currentPage = 1;
      _hasMoreData = true;
    });

    switch (selectedButton) {
      case 1:
        _news.clear();
        getNews(isRefresh: true);
        break;
      case 2:
        _events.clear();
        getEvents(isRefresh: true);
        break;
    }
  }

  void _filterByJobType(String jobType) {
    setState(() {
      _selectedJobType = jobType;
      _currentPage = 1;
      _hasMoreData = true;
    });

    _jobs.clear();
    getJobs(isRefresh: true);
  }



  String formatEventDateTime(String? date, String? time) {
    if (date == null || time == null) return 'Date/time unavailable';

    try {
      // Combine date and time into a single DateTime
      final dateTime = DateTime.parse('$date $time');

      // Format the datetime
      final formatted =
          DateFormat('EEEE MMMM d, y \'at\' HH:mm').format(dateTime);
      return formatted;
    } catch (e) {
      return 'Invalid date/time';
    }
  }


  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();

    final userString = prefs.getString('user');
    final userId = prefs.getString('user_id');

    if (userString != null) {
      final user = jsonDecode(userString);
      setState(() {
        _user = user;
        _userId = userId;
      });
    } else {
      print('No user found. Redirecting...');
    }
  }

 Future<void> getNews({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _isLoadingNews = true;
      });
    }

    try {
      final response = await ApiService.getNews(
        limit: 10,
        page: _currentPage,
        search: _searchQuery,
        category_id: _selectedCategoryId,
      );
      if (response.statusCode == 200) {
        final responseData = response.data;
        final newData = responseData['data'] ?? [];
        final pagination = responseData['pagination'];

        setState(() {
          if (isRefresh || _currentPage == 1) {
            _news = newData;
          } else {
            _news.addAll(newData);
          }
          _hasMoreData = pagination['hasNextPage'] ?? false;
          _isLoadingNews = false;
          _isLoadingMore = false;
        });
        print('News loaded: ${_news.length}');
      } else {
        print('Error getting news: ${response.statusCode}');
        setState(() {
          _isLoadingNews = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      print('Error fetching news: $e');
      setState(() {
        _isLoadingNews = false;
        _isLoadingMore = false;
      });
    }
  }
  // get news categories
  Future<void> getNewsCategories() async {
    try {
      final response = await ApiService.getNewsCategories();
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _newsCategories = responseData['data'] ?? [];
          _isLoadingNewsCategories = false;
        });
        print('News Categories loaded: ${_newsCategories.length}');
      } else {
        print('Error getting news categories: ${response.statusCode}');
        setState(() {
          _isLoadingNewsCategories = false;
        });
      }
    } catch (e) {
      print('Error fetching news categories: $e');
      setState(() {
        _isLoadingNewsCategories = false;
      });
    }
  }

  // get events categories
  Future<void> getEventCategories() async {
    try {
      final response = await ApiService.getEventCategories();
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _eventCategories = responseData['data'] ?? [];
          _isLoadingEventCategories = false;
        });
        print('Event Categories loaded: ${_eventCategories.length}');
      } else {
        print('Error getting event categories: ${response.statusCode}');
        setState(() {
          _isLoadingEventCategories = false;
        });
      }
    } catch (e) {
      print('Error fetching event categories: $e');
      setState(() {
        _isLoadingEventCategories = false;
      });
    }
  }

// get job types
  Future<void> getJobTypes() async {
    try {
      final response = await ApiService.getJobTypes();
      if (response.statusCode == 200) {
        final responseData = response.data;
        setState(() {
          _jobTypes = responseData['data'] ?? [];
          _isLoadingJobTypes = false;
        });
        print('Job Types loaded: ${_jobTypes.length}');
      } else {
        print('Error getting job types: ${response.statusCode}');
        setState(() {
          _isLoadingJobTypes = false;
        });
      }
    } catch (e) {
      print('Error fetching job types: $e');
      setState(() {
        _isLoadingJobTypes = false;
      });
    }
  }

 Future<void> getEvents({bool isRefresh = false}) async {
    if (isRefresh) {
      setState(() {
        _isLoadingEvents = true;
      });
    }

    try {
      final response = await ApiService.getEvents(
        limit: 10,
        page: _currentPage,
        search: _searchQuery,
        category_id: _selectedCategoryId,
      );
      if (response.statusCode == 200) {
        final responseData = response.data;
        final newData = responseData['data'] ?? [];
        final pagination = responseData['pagination'];

        setState(() {
          if (isRefresh || _currentPage == 1) {
            _events = newData;
          } else {
            _events.addAll(newData);
          }
          _hasMoreData = pagination['hasNextPage'] ?? false;
          _isLoadingEvents = false;
          _isLoadingMore = false;
        });
        print('Events loaded: ${_events.length}');
      } else {
        print('Error getting events: ${response.statusCode}');
        setState(() {
          _isLoadingEvents = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      print('Error fetching events: $e');
      setState(() {
        _isLoadingEvents = false;
        _isLoadingMore = false;
      });
    }
  }

  Future<void> getJobs({bool isRefresh = false}) async {
    if (_userId == null) return;

    if (isRefresh) {
      setState(() {
        _isLoadingJobs = true;
      });
    }

    try {
      final response = await ApiService.getRecommendedJobs(
        userId: _userId!,
        page: _currentPage,
        limit: 10,
        search: _searchQuery.isEmpty ? null : _searchQuery,
        jobType: _selectedJobType.isEmpty ? null : _selectedJobType,
      );
      if (response.statusCode == 200) {
        final responseData = response.data;
        final newData = responseData['data'] ?? [];
        final pagination = responseData['pagination'];

        setState(() {
          if (isRefresh || _currentPage == 1) {
            _jobs = newData;
          } else {
            _jobs.addAll(newData);
          }
          _hasMoreData = pagination['hasNextPage'] ?? false;
          _isLoadingJobs = false;
          _isLoadingMore = false;
        });
        print('Jobs loaded: ${_jobs.length}');
      } else {
        print('Error getting jobs: ${response.statusCode}');
        setState(() {
          _isLoadingJobs = false;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      print('Error fetching jobs: $e');
      setState(() {
        _isLoadingJobs = false;
        _isLoadingMore = false;
      });
    }
  }
  Future<void> _toggleNewsFavorite(
    String newsId,
  ) async {
    if (isLoading) return; // Prevent multiple calls

    setState(() {
      isLoading = true;
    });

    try {
      if (isFavorite) {
        // Remove from favorites
        final response = await ApiService.removeNewsFromUserFavorites(
          newsId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            isFavorite = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Removed from favourites!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to remove from favourites',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Add to favorites
        final response = await ApiService.addNewsToUserFavorites(
          newsId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            isFavorite = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Added to favourites!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to add to favourites',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'An error occurred. Please try again.',
            style: TextStyle(fontFamily: "Satoshi"),
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 2),
        ),
      );
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _checkIfUserLikedEvent(String eventId) async {
    try {
      final response = await ApiService.checkIfUserLikedEvent(
        eventId,
        _userId!,
      );

      if (response.statusCode == 200) {
        setState(() {
          _isLiked = response.data['is_liked'] ?? false;
        });
      }
    } catch (e) {
      print('Error checking if user liked event: $e');
    }
  }

  Future<void> _toggleLike(String eventId) async {
    if (_isLiking) return;

    setState(() {
      _isLiking = true;
    });

    try {
      if (_isLiked) {
        final response = await ApiService.userToUnLikeEvent(
          eventId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            _isLiked = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event unliked!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to unlike event',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      } else {
        final response = await ApiService.userToLikeEvent(
          eventId,
          _userId!,
        );

        if (response.statusCode == 200) {
          setState(() {
            _isLiked = true;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                response.data['message'] ?? 'Event liked!',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Color(0xFF1F41BB),
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to like event',
                style: TextStyle(fontFamily: "Satoshi"),
              ),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 2),
            ),
          );
        }
        ;
      }
    } catch (e) {
      setState(() {
        _isLiking = false;
      });
    }
  }

 Widget _buildSearchAndFilter() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search...',
              prefixIcon: Icon(Icons.search),
              suffixIcon: IconButton(
                icon: Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  _performSearch();
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide(color: Color(0xFF1F41BB)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
                borderSide: BorderSide(color: Color(0xFF1F41BB)),
              ),
            ),
            onSubmitted: (value) => _performSearch(),
          ),

          SizedBox(height: 10),

          // Filter options based on selected tab
          if (selectedButton == 0) // Jobs
            _buildJobTypeFilter()
          else if (selectedButton == 1) // News
            _buildCategoryFilter(_newsCategories, 'News Categories')
          else if (selectedButton == 2) // Events
            _buildCategoryFilter(_eventCategories, 'Event Categories'),
        ],
      ),
    );
  }

  Widget _buildJobTypeFilter() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('All', '', _selectedJobType),
          ..._jobTypes.map((type) => _buildFilterChip(type, type, _selectedJobType)),
        ],
      ),
    );
  }


  Widget _buildCategoryFilter(List<dynamic> categories, String title) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip('All', '', _selectedCategoryId),
          ...categories.map((category) => _buildFilterChip(
            category['name'] ?? category['category_name'] ?? 'Unknown',
            category['id'] ?? '',
            _selectedCategoryId,
          )),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, String selectedValue) {
    bool isSelected = selectedValue == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          if (selectedButton == 0) {
            _filterByJobType(value);
          } else {
            _filterByCategory(value);
          }
        },
        backgroundColor: Colors.white,
        selectedColor: Color(0xFF1F41BB),
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Color(0xFF1F41BB),
          fontFamily: "Satoshi",
        ),
        side: BorderSide(color: Color(0xFF1F41BB)),
      ),
    );
  }

  Widget _buildDataList() {
    switch (selectedButton) {
      case 0:
        return _buildJobsList();
      case 1:
        return _buildNewsList();
      case 2:
        return _buildEventsList();
      default:
        return Container();
    }
  }

Widget _buildJobsList() {
    if (_isLoadingJobs && _jobs.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_jobs.isEmpty) {
      return Center(
        child: Text(
          'No jobs found.',
          style: TextStyle(fontFamily: "Satoshi", fontSize: 16),
        ),
      );
    }

    return Column(
      children: [
        ..._jobs.map((job) => _buildJobCard(job)).toList(),
        if (_isLoadingMore)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildNewsList() {
    if (_isLoadingNews && _news.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_news.isEmpty) {
      return Center(
        child: Text(
          'No news found.',
          style: TextStyle(fontFamily: "Satoshi", fontSize: 16),
        ),
      );
    }

    return Column(
      children: [
        ..._news.map((news) => _buildNewsCard(news)).toList(),
        if (_isLoadingMore)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildEventsList() {
    if (_isLoadingEvents && _events.isEmpty) {
      return Center(child: CircularProgressIndicator());
    }

    if (_events.isEmpty) {
      return Center(
        child: Text(
          'No events found.',
          style: TextStyle(fontFamily: "Satoshi", fontSize: 16),
        ),
      );
    }

    return Column(
      children: [
        ..._events.map((event) => _buildEventCard(event)).toList(),
        if (_isLoadingMore)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }

  Widget _buildJobCard(dynamic job) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
      child: Card(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: InkWell(
          onTap: () {
            // Navigate to job details
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: job['company_logo'] != null
                            ? NetworkImage(job['company_logo'])
                            : AssetImage('assets/event.jpg') as ImageProvider,
                        fit: BoxFit.cover,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            job['title'] ?? 'Job Title',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: "Satoshi",
                              fontSize: 16,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            job['company_name'] ?? 'Company',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                              fontSize: 14,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            job['location'] ?? 'Location',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      job['job_type'] ?? 'Full-time',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F41BB),
                        fontFamily: "Satoshi",
                      ),
                    ),
                    Text(
                      job['salary'] ?? 'Salary not specified',
                      style: TextStyle(
                        color: Colors.black54,
                        fontFamily: "Satoshi",
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNewsCard(dynamic news) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
      child: Card(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => NewsDetailsScreen(news: news, userId: _userId!),
              ),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: news['image_url'] != null
                            ? NetworkImage(news['image_url'])
                            : AssetImage('assets/event.jpg') as ImageProvider,
                        fit: BoxFit.cover,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            news['title'] ?? 'News Title',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: "Satoshi",
                              fontSize: 16,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            news['story'] ?? 'No description available',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      news['category'] ?? 'News',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F41BB),
                        fontFamily: "Satoshi",
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          'By ${news['author'] ?? 'Unknown'}',
                          style: TextStyle(
                            color: Colors.black54,
                            fontFamily: "Satoshi",
                            fontSize: 12,
                          ),
                        ),
                        SizedBox(width: 5),
                        GestureDetector(
                          onTap: () => _toggleNewsFavorite(news['id']),
                          child: Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: Color(0xFF1F41BB),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


Widget _buildEventCard(dynamic event) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
      child: Card(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        child: InkWell(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => EventDetailsScreen(event: event, userId: _userId!),
              ),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: event['image_url'] != null
                            ? NetworkImage(event['image_url'])
                            : AssetImage('assets/event.jpg') as ImageProvider,
                        fit: BoxFit.cover,
                      ),
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event['title'] ?? 'Event Title',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontFamily: "Satoshi",
                              fontSize: 16,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4),
                          Text(
                            event['short_desc'] ?? 'No description available',
                            style: TextStyle(
                              color: Colors.black54,
                              fontFamily: "Satoshi",
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      event['category'] ?? 'Event',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F41BB),
                        fontFamily: "Satoshi",
                      ),
                    ),
                    Text(
                      formatEventDateTime(event['event_date'], event['event_time']),
                      style: TextStyle(
                        color: Colors.black54,
                        fontFamily: "Satoshi",
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }




  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        title: Text(
          'Discover',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Row of Buttons
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildCategoryButton(0, 'Jobs', 'assets/bar.svg'),
                    SizedBox(width: 10),
                    _buildCategoryButton(1, 'News', 'assets/fire.svg'),
                    SizedBox(width: 10),
                    _buildCategoryButton(2, 'Events', 'assets/airplay.svg'),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Card below the buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Card(
                elevation: 2,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // Image on the left
                        Container(
                          width: 100, // Fixed width for the image
                          height: 100,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/event.jpg'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15),
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                        ),

                        // Description on the right
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Row below the description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Sports',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F41BB),
                              fontFamily: "Satoshi",
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '20',
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontFamily: "Satoshi",
                                ),
                              ),
                              SizedBox(width: 5),
                              Icon(
                                Icons.favorite, // Filled favorite icon
                                color: Color(0xFF1F41BB),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(
              width: 10,
            ),
            // Card below the buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Card(
                elevation: 2,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // Image on the left
                        Container(
                          width: 100, // Fixed width for the image
                          height: 100,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/event.jpg'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15),
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                        ),

                        // Description on the right
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Row below the description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Sports',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F41BB),
                              fontFamily: "Satoshi",
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '20',
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontFamily: "Satoshi",
                                ),
                              ),
                              SizedBox(width: 5),
                              Icon(
                                Icons.favorite, // Filled favorite icon
                                color: Color(0xFF1F41BB),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(
              width: 10,
            ),
            // Card below the buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Card(
                elevation: 2,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // Image on the left
                        Container(
                          width: 100, // Fixed width for the image
                          height: 100,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/event.jpg'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15),
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                        ),

                        // Description on the right
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Row below the description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Sports',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F41BB),
                              fontFamily: "Satoshi",
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '20',
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontFamily: "Satoshi",
                                ),
                              ),
                              SizedBox(width: 5),
                              Icon(
                                Icons.favorite, // Filled favorite icon
                                color: Color(0xFF1F41BB),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(
              width: 10,
            ),
            // Card below the buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Card(
                elevation: 2,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        // Image on the left
                        Container(
                          width: 100, // Fixed width for the image
                          height: 100,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage('assets/event.jpg'),
                              fit: BoxFit.cover,
                            ),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(15),
                              bottomLeft: Radius.circular(15),
                              bottomRight: Radius.circular(15),
                              topRight: Radius.circular(15),
                            ),
                          ),
                        ),

                        // Description on the right
                        Expanded(
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              'Description about the card goes here. You can add details about the festival, event, or offer.',
                              style: TextStyle(
                                color: Colors.black54,
                                fontFamily: "Satoshi",
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Row below the description
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8.0, vertical: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Sports',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1F41BB),
                              fontFamily: "Satoshi",
                            ),
                          ),
                          Row(
                            children: [
                              Text(
                                '20',
                                style: TextStyle(
                                  color: Colors.black54,
                                  fontFamily: "Satoshi",
                                ),
                              ),
                              SizedBox(width: 5),
                              Icon(
                                Icons.favorite, // Filled favorite icon
                                color: Color(0xFF1F41BB),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryButton(int index, String title, String iconPath) {
    return ElevatedButton(
      onPressed: () => onButtonPressed(index),
      style: ElevatedButton.styleFrom(
        foregroundColor:
            selectedButton == index ? Colors.white : Color(0xFF1F41BB),
        backgroundColor:
            selectedButton == index ? Color(0xFF1F41BB) : Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        side: BorderSide(color: Color(0xFF1F41BB)),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(iconPath, width: 20, height: 20),
          SizedBox(width: 8),
          Text(title),
        ],
      ),
    );
  }
}
