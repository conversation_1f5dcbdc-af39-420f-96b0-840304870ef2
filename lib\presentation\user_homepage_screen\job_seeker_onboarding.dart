import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'package:tareek/services/api_service.dart';
import 'package:file_picker/file_picker.dart';


import '../../widgets/bottom_navigation.dart';

// Data Models
class JobSeekerProfile {
  String profession;
  int yearsExperience;
  List<Skills> skills;
  String aboutMe;
  List<String> interests;
  String resumeURL;
  String portfolioURL;
  String linkedinURL;
  List<Education> education;
  List<WorkExperience> workHistory;
  String location;
  String image;

  JobSeekerProfile({
    this.profession = '',
    this.yearsExperience = 0,
    this.skills = const [],
    this.aboutMe = '',
    this.interests = const [],
    this.resumeURL = '',
    this.portfolioURL = '',
    this.linkedinURL = '',
    this.education = const [],
    this.workHistory = const [],
    this.location = '',
    this.image = '',
  });
}

class Education {
  String degree;
  String institution;
  String fieldOfStudy;
  DateTime startDate;
  DateTime endDate;

  Education({
    this.degree = '',
    this.institution = '',
    this.fieldOfStudy = '',
    DateTime? startDate,
    DateTime? endDate,
  })  : startDate = startDate ?? DateTime.now(),
        endDate = endDate ?? DateTime.now();
}

class WorkExperience {
  String title;
  String company;
  String description;
  DateTime startDate;
  DateTime? endDate;
  bool currentJob;

  WorkExperience({
    this.title = '',
    this.company = '',
    this.description = '',
    DateTime? startDate,
    this.endDate,
    this.currentJob = false,
  }) : startDate = startDate ?? DateTime.now();
}

class Skills {
  String name;
  int level;
  String proficiency;

  Skills({
    this.name = '',
    this.level = 1,
    this.proficiency = 'Beginner',
  });
}

class JobSeekerOnboardingScreen extends StatefulWidget {
  @override
  _JobSeekerOnboardingScreenState createState() =>
      _JobSeekerOnboardingScreenState();
}

class _JobSeekerOnboardingScreenState extends State<JobSeekerOnboardingScreen> {
  final PageController _pageController = PageController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  JobSeekerProfile profile = JobSeekerProfile();
  List<Education> educationList = [Education()];
  List<WorkExperience> workExperienceList = [WorkExperience()];
  List<Skills> skillsList = [Skills()];
  List<String> interestsList = [''];

  File? _profileImage;
    File? _resumeFile;
  final ImagePicker _picker = ImagePicker();

  int currentPage = 0;
  final int totalPages = 6;
  String? _userId;

  // Controllers
  final TextEditingController professionController = TextEditingController();
  final TextEditingController yearsController = TextEditingController();
  final TextEditingController aboutMeController = TextEditingController();
  final TextEditingController portfolioURLController = TextEditingController();
  final TextEditingController linkedinURLController = TextEditingController();
  final TextEditingController locationController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _pageController.dispose();
    professionController.dispose();
    yearsController.dispose();
    aboutMeController.dispose();
    portfolioURLController.dispose();
    linkedinURLController.dispose();
    locationController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();

    final userId = prefs.getString('user_id');

    setState(() {
      _userId = userId;
    });
  }

  Future<void> _pickImage() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _profileImage = File(image.path);
        profile.image = image.path;
      });
    }
  }

Future<void> _pickResumeFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx'],
      allowMultiple: false,
    );

    if (result != null && result.files.single.path != null) {
      setState(() {
        _resumeFile = File(result.files.single.path!);
        profile.resumeURL = result.files.single.path!;
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Resume uploaded: ${result.files.single.name}'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
  void _nextPage() {
    if (currentPage < totalPages - 1) {
      _pageController.nextPage(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (currentPage > 0) {
      _pageController.previousPage(
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _submitProfile() async {
    if (_formKey.currentState!.validate()) {
      // Update profile with all data
      profile.profession = professionController.text;
      profile.yearsExperience = int.tryParse(yearsController.text) ?? 0;
      profile.aboutMe = aboutMeController.text;
      profile.portfolioURL = portfolioURLController.text;
      profile.linkedinURL = linkedinURLController.text;
      profile.location = locationController.text;
      profile.education = educationList;
      profile.workHistory = workExperienceList;
      profile.skills = skillsList;
      profile.interests =
          interestsList.where((interest) => interest.isNotEmpty).toList();
      profile.image = _profileImage?.path ?? '';
      profile.resumeURL = _resumeFile?.path ?? '';

      // Send profile data to server
      final response =
          await ApiService.createJobSeekerProfile(profile, _userId!);

      if (response.statusCode == 200) {
        print('Profile created successfully: ${response.data}');
        // Show success message
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text('Profile Created Successfully!'),
              content: Text(response.data['message'] ??
                  'Your job seeker profile has been created.'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const BottomNavBar(),
                      ),
                    );
                  },
                  child: Text('Continue'),
                ),
              ],
            );
          },
        );
      } else {
        print('Profile creation failed: ${response.data}');
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text(response.data['message'] ?? 'Profile creation failed'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Job Seeker Profile Setup', style: TextStyle(color: Colors.white, fontSize: 20)),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // Progress Indicator
            Container(
              padding: EdgeInsets.all(16),
              child: LinearProgressIndicator(
                value: (currentPage + 1) / totalPages,
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
              ),
            ),
            Text(
              'Step ${currentPage + 1} of $totalPages',
              style: TextStyle(color: Colors.grey.shade600),
            ),

            // Page View
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    currentPage = index;
                  });
                },
                children: [
                  _buildBasicInfoPage(),
                  _buildProfileImagePage(),
                  _buildEducationPage(),
                  _buildWorkExperiencePage(),
                  _buildSkillsPage(),
                  _buildAdditionalInfoPage(),
                ],
              ),
            ),

            // Navigation Buttons
            Container(
              padding: EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (currentPage > 0)
                    ElevatedButton(
                      onPressed: _previousPage,
                      child: Text('Previous'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade400,
                      ),
                    )
                  else
                    SizedBox(),
                  ElevatedButton(
                    onPressed: currentPage == totalPages - 1
                        ? _submitProfile
                        : _nextPage,
                    child: Text(currentPage == totalPages - 1
                        ? 'Complete Profile'
                        : 'Next'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoPage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 20),
          TextFormField(
            controller: professionController,
            decoration: InputDecoration(
              labelText: 'Profession *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.work),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your profession';
              }
              return null;
            },
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: yearsController,
            decoration: InputDecoration(
              labelText: 'Years of Experience *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.timeline),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter years of experience';
              }
              if (int.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              return null;
            },
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: locationController,
            decoration: InputDecoration(
              labelText: 'Location *',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.location_on),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your location';
              }
              return null;
            },
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: aboutMeController,
            decoration: InputDecoration(
              labelText: 'About Me',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.person),
            ),
            maxLines: 4,
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImagePage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Profile Image',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 20),
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(75),
                border: Border.all(color: Colors.blue.shade600, width: 2),
              ),
              child: _profileImage != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(75),
                      child: Image.file(
                        _profileImage!,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Icon(
                      Icons.add_a_photo,
                      size: 50,
                      color: Colors.grey.shade600,
                    ),
            ),
          ),
          SizedBox(height: 16),
          Text(
            'Tap to add profile image',
            style: TextStyle(color: Colors.grey.shade600),
          ),
          SizedBox(height: 20),

          Text(
            'Resume & Social Links',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),


          SizedBox(height: 16),

             Container(
            width: double.infinity,
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Icon(
                  _resumeFile != null ? Icons.check_circle : Icons.description,
                  size: 40,
                  color:
                      _resumeFile != null ? Colors.green : Colors.grey.shade600,
                ),
                SizedBox(height: 8),
                Text(
                  _resumeFile != null ? 'Resume Uploaded' : 'Upload Resume',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: _resumeFile != null
                        ? Colors.green
                        : Colors.grey.shade600,
                  ),
                ),
                if (_resumeFile != null) ...[
                  SizedBox(height: 4),
                  Text(
                    _resumeFile!.path.split('/').last,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                SizedBox(height: 8),
                ElevatedButton.icon(
                  onPressed: _pickResumeFile,
                  icon:
                      Icon(_resumeFile != null ? Icons.refresh : Icons.upload),
                  label: Text(
                      _resumeFile != null ? 'Change Resume' : 'Select Resume'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Supported formats: PDF, DOC, DOCX',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16),
          TextFormField(
            controller: portfolioURLController,
            decoration: InputDecoration(
              labelText: 'Portfolio URL',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.web),
            ),
          ),
          SizedBox(height: 16),
          TextFormField(
            controller: linkedinURLController,
            decoration: InputDecoration(
              labelText: 'LinkedIn URL',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.link),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEducationPage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Education',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    educationList.add(Education());
                  });
                },
                icon: Icon(Icons.add),
                tooltip: 'Add Education',
              ),
            ],
          ),
          SizedBox(height: 20),
          ...educationList.asMap().entries.map((entry) {
            int index = entry.key;
            Education education = entry.value;

            return Card(
              margin: EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Education ${index + 1}'),
                        if (educationList.length > 1)
                          IconButton(
                            onPressed: () {
                              setState(() {
                                educationList.removeAt(index);
                              });
                            },
                            icon: Icon(Icons.delete, color: Colors.red),
                          ),
                      ],
                    ),
                    SizedBox(height: 16),
                    TextFormField(
                      initialValue: education.degree,
                      decoration: InputDecoration(
                        labelText: 'Degree',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        education.degree = value;
                      },
                    ),
                    SizedBox(height: 16),
                    TextFormField(
                      initialValue: education.institution,
                      decoration: InputDecoration(
                        labelText: 'Institution',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        education.institution = value;
                      },
                    ),
                    SizedBox(height: 16),
                    TextFormField(
                      initialValue: education.fieldOfStudy,
                      decoration: InputDecoration(
                        labelText: 'Field of Study',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        education.fieldOfStudy = value;
                      },
                    ),
                    SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(
                              labelText: 'Start Date',
                              border: OutlineInputBorder(),
                            ),
                            readOnly: true,
                            onTap: () async {
                              DateTime? date = await showDatePicker(
                                context: context,
                                initialDate: education.startDate,
                                firstDate: DateTime(1970),
                                lastDate: DateTime.now(),
                              );
                              if (date != null) {
                                setState(() {
                                  education.startDate = date;
                                });
                              }
                            },
                            controller: TextEditingController(
                              text:
                                  '${education.startDate.day}/${education.startDate.month}/${education.startDate.year}',
                            ),
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(
                              labelText: 'End Date',
                              border: OutlineInputBorder(),
                            ),
                            readOnly: true,
                            onTap: () async {
                              DateTime? date = await showDatePicker(
                                context: context,
                                initialDate: education.endDate,
                                firstDate: DateTime(1970),
                                lastDate: DateTime.now(),
                              );
                              if (date != null) {
                                setState(() {
                                  education.endDate = date;
                                });
                              }
                            },
                            controller: TextEditingController(
                              text:
                                  '${education.endDate.day}/${education.endDate.month}/${education.endDate.year}',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildWorkExperiencePage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Work Experience',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    workExperienceList.add(WorkExperience());
                  });
                },
                icon: Icon(Icons.add),
                tooltip: 'Add Work Experience',
              ),
            ],
          ),
          SizedBox(height: 20),
          ...workExperienceList.asMap().entries.map((entry) {
            int index = entry.key;
            WorkExperience work = entry.value;

            return Card(
              margin: EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Experience ${index + 1}'),
                        if (workExperienceList.length > 1)
                          IconButton(
                            onPressed: () {
                              setState(() {
                                workExperienceList.removeAt(index);
                              });
                            },
                            icon: Icon(Icons.delete, color: Colors.red),
                          ),
                      ],
                    ),
                    SizedBox(height: 16),
                    TextFormField(
                      initialValue: work.title,
                      decoration: InputDecoration(
                        labelText: 'Job Title',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        work.title = value;
                      },
                    ),
                    SizedBox(height: 16),
                    TextFormField(
                      initialValue: work.company,
                      decoration: InputDecoration(
                        labelText: 'Company',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        work.company = value;
                      },
                    ),
                    SizedBox(height: 16),
                    TextFormField(
                      initialValue: work.description,
                      decoration: InputDecoration(
                        labelText: 'Description',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      onChanged: (value) {
                        work.description = value;
                      },
                    ),
                    SizedBox(height: 16),
                    CheckboxListTile(
                      title: Text('Current Job'),
                      value: work.currentJob,
                      onChanged: (bool? value) {
                        setState(() {
                          work.currentJob = value ?? false;
                        });
                      },
                    ),
                    SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            decoration: InputDecoration(
                              labelText: 'Start Date',
                              border: OutlineInputBorder(),
                            ),
                            readOnly: true,
                            onTap: () async {
                              DateTime? date = await showDatePicker(
                                context: context,
                                initialDate: work.startDate,
                                firstDate: DateTime(1970),
                                lastDate: DateTime.now(),
                              );
                              if (date != null) {
                                setState(() {
                                  work.startDate = date;
                                });
                              }
                            },
                            controller: TextEditingController(
                              text:
                                  '${work.startDate.day}/${work.startDate.month}/${work.startDate.year}',
                            ),
                          ),
                        ),
                        SizedBox(width: 16),
                        Expanded(
                          child: work.currentJob
                              ? Container(
                                  height: 56,
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text('Present'),
                                )
                              : TextFormField(
                                  decoration: InputDecoration(
                                    labelText: 'End Date',
                                    border: OutlineInputBorder(),
                                  ),
                                  readOnly: true,
                                  onTap: () async {
                                    DateTime? date = await showDatePicker(
                                      context: context,
                                      initialDate:
                                          work.endDate ?? DateTime.now(),
                                      firstDate: DateTime(1970),
                                      lastDate: DateTime.now(),
                                    );
                                    if (date != null) {
                                      setState(() {
                                        work.endDate = date;
                                      });
                                    }
                                  },
                                  controller: TextEditingController(
                                    text: work.endDate != null
                                        ? '${work.endDate!.day}/${work.endDate!.month}/${work.endDate!.year}'
                                        : '',
                                  ),
                                ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildSkillsPage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Skills',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    skillsList.add(Skills());
                  });
                },
                icon: Icon(Icons.add),
                tooltip: 'Add Skill',
              ),
            ],
          ),
          SizedBox(height: 20),
          ...skillsList.asMap().entries.map((entry) {
            int index = entry.key;
            Skills skill = entry.value;

            return Card(
              margin: EdgeInsets.only(bottom: 16),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Skill ${index + 1}'),
                        if (skillsList.length > 1)
                          IconButton(
                            onPressed: () {
                              setState(() {
                                skillsList.removeAt(index);
                              });
                            },
                            icon: Icon(Icons.delete, color: Colors.red),
                          ),
                      ],
                    ),
                    SizedBox(height: 16),
                    TextFormField(
                      initialValue: skill.name,
                      decoration: InputDecoration(
                        labelText: 'Skill Name',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        skill.name = value;
                      },
                    ),
                    SizedBox(height: 16),
                    Text('Level: ${skill.level}/10'),
                    Slider(
                      value: skill.level.toDouble(),
                      min: 1,
                      max: 10,
                      divisions: 9,
                      onChanged: (double value) {
                        setState(() {
                          skill.level = value.toInt();
                          // Update proficiency based on level
                          if (value <= 3) {
                            skill.proficiency = 'Beginner';
                          } else if (value <= 6) {
                            skill.proficiency = 'Intermediate';
                          } else {
                            skill.proficiency = 'Advanced';
                          }
                        });
                      },
                    ),
                    Text('Proficiency: ${skill.proficiency}'),
                  ],
                ),
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoPage() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Additional Information',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Interests',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    interestsList.add('');
                  });
                },
                icon: Icon(Icons.add),
                tooltip: 'Add Interest',
              ),
            ],
          ),
          SizedBox(height: 16),
          ...interestsList.asMap().entries.map((entry) {
            int index = entry.key;
            String interest = entry.value;

            return Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      initialValue: interest,
                      decoration: InputDecoration(
                        labelText: 'Interest ${index + 1}',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        interestsList[index] = value;
                      },
                    ),
                  ),
                  if (interestsList.length > 1)
                    IconButton(
                      onPressed: () {
                        setState(() {
                          interestsList.removeAt(index);
                        });
                      },
                      icon: Icon(Icons.delete, color: Colors.red),
                    ),
                ],
              ),
            );
          }).toList(),
          SizedBox(height: 32),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Profile Summary',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('Profession: ${professionController.text}'),
                Text('Experience: ${yearsController.text} years'),
                Text('Location: ${locationController.text}'),
                Text('Education entries: ${educationList.length}'),
                Text('Work experiences: ${workExperienceList.length}'),
                Text('Skills: ${skillsList.length}'),
                Text(
                    'Interests: ${interestsList.where((i) => i.isNotEmpty).length}'),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
